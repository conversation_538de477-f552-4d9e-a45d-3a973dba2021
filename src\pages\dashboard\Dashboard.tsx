import React, { useEffect, useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { Calendar, Users, Clock, MessageSquare, Activity, ChevronRight } from 'lucide-react';

interface AppointmentPreview {
  id: string;
  nurseName: string;
  date: string;
  time: string;
  status: 'upcoming' | 'completed' | 'canceled';
}

const Dashboard: React.FC = () => {
  const { currentUser } = useAuth();
  const [loading, setLoading] = useState(true);
  const [upcomingAppointments, setUpcomingAppointments] = useState<AppointmentPreview[]>([]);
  const [recentMessages, setRecentMessages] = useState<any[]>([]);
  
  useEffect(() => {
    // Simulating API calls
    setTimeout(() => {
      setUpcomingAppointments([
        {
          id: '1',
          nurseName: '<PERSON>',
          date: '2025-06-15',
          time: '10:00 AM',
          status: 'upcoming'
        },
        {
          id: '2',
          nurseName: '<PERSON>',
          date: '2025-06-18',
          time: '2:30 PM',
          status: 'upcoming'
        }
      ]);
      
      setRecentMessages([
        {
          id: '1',
          sender: 'Dr. <PERSON>',
          message: 'Your test results look good. Let\'s discuss in our next appointment.',
          time: '2 hours ago',
          unread: true
        },
        {
          id: '2',
          sender: 'Nurse Sarah',
          message: 'Remember to take your medication as prescribed.',
          time: 'Yesterday',
          unread: false
        }
      ]);
      
      setLoading(false);
    }, 1000);
  }, []);

  const formatDate = (dateString: string) => {
    const options: Intl.DateTimeFormatOptions = { weekday: 'short', month: 'short', day: 'numeric' };
    return new Date(dateString).toLocaleDateString('en-US', options);
  };

  return (
    <div>
      <header className="mb-8">
        <h1 className="text-2xl font-bold text-neutral-800 mb-2">
          Welcome, {currentUser?.displayName || 'Patient'}
        </h1>
        <p className="text-neutral-600">
          Here's an overview of your healthcare activities
        </p>
      </header>

      {loading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[1, 2, 3].map((item) => (
            <div key={item} className="h-48 bg-white rounded-lg animate-pulse" />
          ))}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Quick Stats */}
          <div className="card bg-white">
            <h2 className="text-lg font-semibold mb-4 flex items-center">
              <Activity size={18} className="text-primary-500 mr-2" />
              Health Summary
            </h2>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-neutral-600">Next Appointment</span>
                <span className="font-medium">
                  {upcomingAppointments.length > 0 
                    ? formatDate(upcomingAppointments[0].date)
                    : 'None scheduled'}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-neutral-600">Recent Visits</span>
                <span className="font-medium">2 in last month</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-neutral-600">Medication Adherence</span>
                <span className="font-medium text-success">98%</span>
              </div>
            </div>
          </div>
          
          {/* Upcoming Appointments */}
          <div className="card bg-white">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold flex items-center">
                <Calendar size={18} className="text-primary-500 mr-2" />
                Upcoming Appointments
              </h2>
              <Link to="/appointments" className="text-sm text-primary-600 hover:text-primary-700 flex items-center">
                View all
                <ChevronRight size={16} />
              </Link>
            </div>
            
            {upcomingAppointments.length > 0 ? (
              <div className="space-y-3">
                {upcomingAppointments.map((appointment) => (
                  <div key={appointment.id} className="p-3 bg-neutral-50 rounded-md">
                    <div className="flex items-center justify-between mb-1">
                      <span className="font-medium">{appointment.nurseName}</span>
                      <span className="text-sm px-2 py-1 bg-primary-100 text-primary-700 rounded-full">
                        {appointment.status}
                      </span>
                    </div>
                    <div className="flex items-center text-neutral-600 text-sm">
                      <Clock size={14} className="mr-1" />
                      {formatDate(appointment.date)} at {appointment.time}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-6 text-neutral-500">
                <Calendar size={24} className="mx-auto mb-2 text-neutral-400" />
                <p>No upcoming appointments</p>
                <Link to="/appointments" className="text-primary-600 hover:text-primary-700 text-sm mt-2 inline-block">
                  Schedule Now
                </Link>
              </div>
            )}
          </div>
          
          {/* Recent Messages */}
          <div className="card bg-white">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold flex items-center">
                <MessageSquare size={18} className="text-primary-500 mr-2" />
                Recent Messages
              </h2>
              <Link to="/chat" className="text-sm text-primary-600 hover:text-primary-700 flex items-center">
                View all
                <ChevronRight size={16} />
              </Link>
            </div>
            
            {recentMessages.length > 0 ? (
              <div className="space-y-3">
                {recentMessages.map((message) => (
                  <Link key={message.id} to={`/chat/${message.id}`} className="block p-3 hover:bg-neutral-50 rounded-md transition-colors">
                    <div className="flex items-center justify-between mb-1">
                      <span className="font-medium">{message.sender}</span>
                      <span className="text-xs text-neutral-500">{message.time}</span>
                    </div>
                    <p className="text-sm text-neutral-600 truncate">
                      {message.message}
                    </p>
                    {message.unread && (
                      <span className="inline-block w-2 h-2 bg-primary-500 rounded-full mt-1"></span>
                    )}
                  </Link>
                ))}
              </div>
            ) : (
              <div className="text-center py-6 text-neutral-500">
                <MessageSquare size={24} className="mx-auto mb-2 text-neutral-400" />
                <p>No recent messages</p>
                <Link to="/chat" className="text-primary-600 hover:text-primary-700 text-sm mt-2 inline-block">
                  Start a conversation
                </Link>
              </div>
            )}
          </div>
        </div>
      )}
      
      {/* Care Team Section */}
      <div className="mt-8">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold flex items-center">
            <Users size={18} className="text-primary-500 mr-2" />
            Your Care Team
          </h2>
        </div>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          {[
            { name: 'Dr. Williams', role: 'Primary Physician', image: 'https://images.pexels.com/photos/5452293/pexels-photo-5452293.jpeg?auto=compress&cs=tinysrgb&w=150' },
            { name: 'Sarah Johnson', role: 'Home Nurse', image: 'https://images.pexels.com/photos/5407206/pexels-photo-5407206.jpeg?auto=compress&cs=tinysrgb&w=150' },
            { name: 'Michael Chen', role: 'Physical Therapist', image: 'https://images.pexels.com/photos/5327585/pexels-photo-5327585.jpeg?auto=compress&cs=tinysrgb&w=150' }
          ].map((member, index) => (
            <div key={index} className="bg-white rounded-lg p-4 flex items-center space-x-4">
              <img 
                src={member.image} 
                alt={member.name} 
                className="w-12 h-12 rounded-full object-cover"
              />
              <div>
                <h3 className="font-medium">{member.name}</h3>
                <p className="text-sm text-neutral-600">{member.role}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Dashboard;