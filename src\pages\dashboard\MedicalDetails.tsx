import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { useForm } from 'react-hook-form';
import { toast } from 'react-hot-toast';
import { FilePlus, AlertCircle, FileText, Pill, Heart, Activity, Merge as Allergens, CalendarCheck, PlusCircle, Trash2, Edit, CheckCircle, XCircle, Loader2 } from 'lucide-react';

interface MedicalFormInputs {
  bloodType: string;
  height: string;
  weight: string;
  allergies: string;
  chronicConditions: string;
  surgeries: string;
  familyHistory: string;
}

interface Medication {
  id: string;
  name: string;
  dosage: string;
  frequency: string;
  startDate: string;
  endDate?: string;
  notes?: string;
}

const MedicalDetails: React.FC = () => {
  const { currentUser } = useAuth();
  const [activeTab, setActiveTab] = useState<'general' | 'medications' | 'records'>('general');
  const [loading, setLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [medications, setMedications] = useState<Medication[]>([]);
  const [editingMedication, setEditingMedication] = useState<Medication | null>(null);
  const [showMedicationForm, setShowMedicationForm] = useState(false);
  
  const { register, handleSubmit, formState: { errors }, setValue } = useForm<MedicalFormInputs>();

  useEffect(() => {
    // Simulate loading medical data
    setTimeout(() => {
      setValue('bloodType', 'A+');
      setValue('height', '175');
      setValue('weight', '70');
      setValue('allergies', 'Penicillin, Peanuts');
      setValue('chronicConditions', 'Hypertension');
      setValue('surgeries', 'Appendectomy (2020)');
      setValue('familyHistory', 'Diabetes (Father), Hypertension (Mother)');
      
      setMedications([
        {
          id: '1',
          name: 'Lisinopril',
          dosage: '10mg',
          frequency: 'Once daily',
          startDate: '2025-01-15',
          notes: 'Take in the morning with food'
        },
        {
          id: '2',
          name: 'Metformin',
          dosage: '500mg',
          frequency: 'Twice daily',
          startDate: '2025-02-10',
          notes: 'Take with meals'
        }
      ]);
      
      setLoading(false);
    }, 1000);
  }, [setValue]);

  const onGeneralSubmit = async (data: MedicalFormInputs) => {
    try {
      setIsSaving(true);
      
      // In a real app, you would save this data to Firestore
      // This is just a simulation
      setTimeout(() => {
        toast.success('Medical information updated successfully');
        setIsSaving(false);
      }, 1000);
    } catch (error) {
      console.error('Error updating medical information:', error);
      toast.error('Failed to update medical information');
      setIsSaving(false);
    }
  };

  const handleAddMedication = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const form = e.currentTarget;
    const formData = new FormData(form);
    
    const newMedication: Medication = {
      id: Date.now().toString(),
      name: formData.get('name') as string,
      dosage: formData.get('dosage') as string,
      frequency: formData.get('frequency') as string,
      startDate: formData.get('startDate') as string,
      endDate: formData.get('endDate') as string || undefined,
      notes: formData.get('notes') as string || undefined
    };
    
    if (editingMedication) {
      // Update existing medication
      setMedications(medications.map(med => 
        med.id === editingMedication.id ? { ...newMedication, id: med.id } : med
      ));
      toast.success('Medication updated successfully');
    } else {
      // Add new medication
      setMedications([...medications, newMedication]);
      toast.success('Medication added successfully');
    }
    
    setEditingMedication(null);
    setShowMedicationForm(false);
    form.reset();
  };

  const handleEditMedication = (medication: Medication) => {
    setEditingMedication(medication);
    setShowMedicationForm(true);
  };

  const handleDeleteMedication = (id: string) => {
    setMedications(medications.filter(med => med.id !== id));
    toast.success('Medication removed successfully');
  };

  return (
    <div>
      <div className="bg-white rounded-lg shadow-sm p-4 mb-6">
        <h1 className="text-2xl font-bold">Medical Information</h1>
        <p className="text-neutral-600">Manage your health records and medication information</p>
      </div>
      
      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        <div className="flex border-b border-neutral-200 overflow-x-auto">
          <button
            className={`px-6 py-3 font-medium text-sm flex items-center whitespace-nowrap ${
              activeTab === 'general'
                ? 'text-primary-600 border-b-2 border-primary-500'
                : 'text-neutral-600 hover:text-neutral-900'
            }`}
            onClick={() => setActiveTab('general')}
          >
            <FileText size={16} className="mr-2" />
            General Information
          </button>
          
          <button
            className={`px-6 py-3 font-medium text-sm flex items-center whitespace-nowrap ${
              activeTab === 'medications'
                ? 'text-primary-600 border-b-2 border-primary-500'
                : 'text-neutral-600 hover:text-neutral-900'
            }`}
            onClick={() => setActiveTab('medications')}
          >
            <Pill size={16} className="mr-2" />
            Medications
          </button>
          
          <button
            className={`px-6 py-3 font-medium text-sm flex items-center whitespace-nowrap ${
              activeTab === 'records'
                ? 'text-primary-600 border-b-2 border-primary-500'
                : 'text-neutral-600 hover:text-neutral-900'
            }`}
            onClick={() => setActiveTab('records')}
          >
            <FilePlus size={16} className="mr-2" />
            Medical Records
          </button>
        </div>
        
        <div className="p-6">
          {loading ? (
            <div className="flex justify-center items-center py-12">
              <Loader2 size={24} className="animate-spin text-primary-500" />
            </div>
          ) : activeTab === 'general' ? (
            <form onSubmit={handleSubmit(onGeneralSubmit)}>
              <div className="mb-6 p-4 bg-blue-50 border border-blue-100 rounded-lg flex items-start">
                <AlertCircle size={20} className="text-blue-500 mr-3 flex-shrink-0 mt-0.5" />
                <p className="text-sm text-blue-700">
                  This information will be shared with your healthcare providers to ensure you receive the best care possible.
                </p>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-semibold mb-4 flex items-center">
                    <Heart size={18} className="text-primary-500 mr-2" />
                    Basic Health Information
                  </h3>
                  
                  <div className="space-y-4">
                    <div>
                      <label htmlFor="bloodType" className="form-label">Blood Type</label>
                      <select
                        id="bloodType"
                        className="form-input"
                        {...register('bloodType')}
                      >
                        <option value="">Select Blood Type</option>
                        <option value="A+">A+</option>
                        <option value="A-">A-</option>
                        <option value="B+">B+</option>
                        <option value="B-">B-</option>
                        <option value="AB+">AB+</option>
                        <option value="AB-">AB-</option>
                        <option value="O+">O+</option>
                        <option value="O-">O-</option>
                      </select>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label htmlFor="height" className="form-label">Height (cm)</label>
                        <input
                          id="height"
                          type="number"
                          className="form-input"
                          {...register('height')}
                        />
                      </div>
                      
                      <div>
                        <label htmlFor="weight" className="form-label">Weight (kg)</label>
                        <input
                          id="weight"
                          type="number"
                          className="form-input"
                          {...register('weight')}
                        />
                      </div>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h3 className="text-lg font-semibold mb-4 flex items-center">
                    <Allergens size={18} className="text-primary-500 mr-2" />
                    Allergies & Conditions
                  </h3>
                  
                  <div className="space-y-4">
                    <div>
                      <label htmlFor="allergies" className="form-label">Allergies</label>
                      <textarea
                        id="allergies"
                        className="form-input"
                        rows={2}
                        placeholder="List any allergies"
                        {...register('allergies')}
                      ></textarea>
                    </div>
                    
                    <div>
                      <label htmlFor="chronicConditions" className="form-label">Chronic Conditions</label>
                      <textarea
                        id="chronicConditions"
                        className="form-input"
                        rows={2}
                        placeholder="List any chronic conditions"
                        {...register('chronicConditions')}
                      ></textarea>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h3 className="text-lg font-semibold mb-4 flex items-center">
                    <CalendarCheck size={18} className="text-primary-500 mr-2" />
                    Medical History
                  </h3>
                  
                  <div className="space-y-4">
                    <div>
                      <label htmlFor="surgeries" className="form-label">Past Surgeries</label>
                      <textarea
                        id="surgeries"
                        className="form-input"
                        rows={3}
                        placeholder="List any past surgeries with dates"
                        {...register('surgeries')}
                      ></textarea>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h3 className="text-lg font-semibold mb-4 flex items-center">
                    <Activity size={18} className="text-primary-500 mr-2" />
                    Family Medical History
                  </h3>
                  
                  <div>
                    <label htmlFor="familyHistory" className="form-label">Family Health Conditions</label>
                    <textarea
                      id="familyHistory"
                      className="form-input"
                      rows={3}
                      placeholder="List any relevant family medical history"
                      {...register('familyHistory')}
                    ></textarea>
                  </div>
                </div>
              </div>
              
              <div className="mt-8 flex justify-end">
                <button
                  type="submit"
                  disabled={isSaving}
                  className="btn btn-primary"
                >
                  {isSaving ? (
                    <Loader2 size={16} className="animate-spin mr-2" />
                  ) : null}
                  Save Medical Information
                </button>
              </div>
            </form>
          ) : activeTab === 'medications' ? (
            <div>
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-lg font-semibold flex items-center">
                  <Pill size={18} className="text-primary-500 mr-2" />
                  Current Medications
                </h3>
                
                {!showMedicationForm && (
                  <button 
                    onClick={() => {
                      setEditingMedication(null);
                      setShowMedicationForm(true);
                    }}
                    className="btn btn-primary flex items-center"
                  >
                    <PlusCircle size={16} className="mr-2" />
                    Add Medication
                  </button>
                )}
              </div>
              
              {showMedicationForm && (
                <div className="bg-neutral-50 p-4 rounded-lg border border-neutral-200 mb-6 animate-fade-in">
                  <h4 className="text-md font-medium mb-4">
                    {editingMedication ? 'Edit Medication' : 'Add New Medication'}
                  </h4>
                  
                  <form onSubmit={handleAddMedication} className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="form-label">Medication Name</label>
                      <input
                        name="name"
                        type="text"
                        required
                        className="form-input"
                        placeholder="e.g., Lisinopril"
                        defaultValue={editingMedication?.name}
                      />
                    </div>
                    
                    <div>
                      <label className="form-label">Dosage</label>
                      <input
                        name="dosage"
                        type="text"
                        required
                        className="form-input"
                        placeholder="e.g., 10mg"
                        defaultValue={editingMedication?.dosage}
                      />
                    </div>
                    
                    <div>
                      <label className="form-label">Frequency</label>
                      <input
                        name="frequency"
                        type="text"
                        required
                        className="form-input"
                        placeholder="e.g., Once daily"
                        defaultValue={editingMedication?.frequency}
                      />
                    </div>
                    
                    <div>
                      <label className="form-label">Start Date</label>
                      <input
                        name="startDate"
                        type="date"
                        required
                        className="form-input"
                        defaultValue={editingMedication?.startDate}
                      />
                    </div>
                    
                    <div>
                      <label className="form-label">End Date (if applicable)</label>
                      <input
                        name="endDate"
                        type="date"
                        className="form-input"
                        defaultValue={editingMedication?.endDate}
                      />
                    </div>
                    
                    <div className="md:col-span-2">
                      <label className="form-label">Notes</label>
                      <textarea
                        name="notes"
                        className="form-input"
                        rows={2}
                        placeholder="Additional instructions or notes"
                        defaultValue={editingMedication?.notes}
                      ></textarea>
                    </div>
                    
                    <div className="md:col-span-2 flex justify-end space-x-2">
                      <button
                        type="button"
                        className="btn btn-outline"
                        onClick={() => {
                          setShowMedicationForm(false);
                          setEditingMedication(null);
                        }}
                      >
                        Cancel
                      </button>
                      <button type="submit" className="btn btn-primary">
                        {editingMedication ? 'Update Medication' : 'Add Medication'}
                      </button>
                    </div>
                  </form>
                </div>
              )}
              
              {medications.length > 0 ? (
                <div className="space-y-4">
                  {medications.map((medication) => (
                    <div 
                      key={medication.id} 
                      className="p-4 border border-neutral-200 rounded-lg hover:bg-neutral-50 transition-colors"
                    >
                      <div className="flex justify-between">
                        <div>
                          <h4 className="font-medium">{medication.name}</h4>
                          <div className="text-sm text-neutral-600 mt-1">
                            {medication.dosage} • {medication.frequency}
                          </div>
                        </div>
                        
                        <div className="flex space-x-2">
                          <button 
                            onClick={() => handleEditMedication(medication)}
                            className="p-1 text-neutral-500 hover:text-primary-500"
                            title="Edit"
                          >
                            <Edit size={16} />
                          </button>
                          <button 
                            onClick={() => handleDeleteMedication(medication.id)}
                            className="p-1 text-neutral-500 hover:text-red-500"
                            title="Delete"
                          >
                            <Trash2 size={16} />
                          </button>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-2 gap-2 mt-3 text-sm">
                        <div>
                          <span className="text-neutral-500">Start Date:</span>{' '}
                          {new Date(medication.startDate).toLocaleDateString()}
                        </div>
                        
                        {medication.endDate && (
                          <div>
                            <span className="text-neutral-500">End Date:</span>{' '}
                            {new Date(medication.endDate).toLocaleDateString()}
                          </div>
                        )}
                      </div>
                      
                      {medication.notes && (
                        <div className="mt-2 text-sm">
                          <span className="text-neutral-500">Notes:</span> {medication.notes}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12 bg-neutral-50 rounded-lg border border-neutral-200">
                  <Pill size={32} className="mx-auto text-neutral-400 mb-2" />
                  <h3 className="text-lg font-medium text-neutral-700 mb-1">No medications added</h3>
                  <p className="text-neutral-500 mb-4">
                    Add your current medications to keep track of your treatment
                  </p>
                  <button 
                    onClick={() => setShowMedicationForm(true)}
                    className="btn btn-primary"
                  >
                    Add Your First Medication
                  </button>
                </div>
              )}
            </div>
          ) : (
            <div>
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-lg font-semibold flex items-center">
                  <FilePlus size={18} className="text-primary-500 mr-2" />
                  Medical Records & Documents
                </h3>
                
                <button className="btn btn-primary flex items-center">
                  <PlusCircle size={16} className="mr-2" />
                  Upload Document
                </button>
              </div>
              
              <div className="text-center py-12 bg-neutral-50 rounded-lg border border-neutral-200">
                <FilePlus size={32} className="mx-auto text-neutral-400 mb-2" />
                <h3 className="text-lg font-medium text-neutral-700 mb-1">No medical records uploaded</h3>
                <p className="text-neutral-500 mb-4">
                  Upload important medical documents like test results, prescriptions, and more
                </p>
                <button className="btn btn-primary">
                  Upload Your First Document
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default MedicalDetails;