import React, { useState } from 'react';
import { Outlet, NavLink, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { 
  Heart, 
  Home, 
  Calendar, 
  MessageSquare, 
  User, 
  FileText, 
  Menu, 
  X, 
  LogOut, 
  Bell 
} from 'lucide-react';
import classNames from 'classnames';

const DashboardLayout: React.FC = () => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const { currentUser, logout } = useAuth();
  const navigate = useNavigate();

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/login');
    } catch (error) {
      console.error('Failed to log out', error);
    }
  };

  const navItems = [
    { to: '/dashboard', icon: <Home size={20} />, label: 'Dashboard' },
    { to: '/appointments', icon: <Calendar size={20} />, label: 'Appointments' },
    { to: '/chat', icon: <MessageSquare size={20} />, label: 'Messages' },
    { to: '/profile', icon: <User size={20} />, label: 'Profile' },
    { to: '/medical-details', icon: <FileText size={20} />, label: 'Medical Details' },
  ];

  const NavItem = ({ to, icon, label }: { to: string, icon: React.ReactNode, label: string }) => (
    <NavLink 
      to={to} 
      className={({ isActive }) => classNames(
        'flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors',
        {
          'bg-primary-500 text-white': isActive,
          'text-neutral-700 hover:bg-neutral-100': !isActive
        }
      )}
      onClick={() => setIsSidebarOpen(false)}
    >
      {icon}
      <span>{label}</span>
    </NavLink>
  );

  return (
    <div className="min-h-screen bg-neutral-50 flex">
      {/* Sidebar - Desktop */}
      <aside className="hidden md:flex flex-col w-64 bg-white border-r border-neutral-200">
        <div className="p-4 border-b border-neutral-200 flex items-center">
          <Heart size={24} className="text-primary-500 mr-2" />
          <h1 className="text-xl font-bold text-primary-700">Home Nurse</h1>
        </div>
        
        <div className="flex-1 px-4 py-6 space-y-1 overflow-y-auto">
          {navItems.map((item) => (
            <NavItem key={item.to} {...item} />
          ))}
        </div>
        
        <div className="p-4 border-t border-neutral-200">
          <button 
            onClick={handleLogout}
            className="flex items-center space-x-3 text-neutral-700 hover:text-primary-600 w-full px-4 py-3 rounded-lg transition-colors"
          >
            <LogOut size={20} />
            <span>Logout</span>
          </button>
        </div>
      </aside>
      
      {/* Mobile Sidebar */}
      <div 
        className={classNames(
          "fixed inset-0 bg-neutral-900/50 z-40 md:hidden transition-opacity duration-300",
          {
            "opacity-100": isSidebarOpen,
            "opacity-0 pointer-events-none": !isSidebarOpen
          }
        )}
        onClick={() => setIsSidebarOpen(false)}
      />
      
      <aside 
        className={classNames(
          "fixed top-0 bottom-0 left-0 w-64 bg-white z-50 transform transition-transform duration-300 ease-in-out md:hidden",
          {
            "translate-x-0": isSidebarOpen,
            "-translate-x-full": !isSidebarOpen
          }
        )}
      >
        <div className="p-4 border-b border-neutral-200 flex items-center justify-between">
          <div className="flex items-center">
            <Heart size={24} className="text-primary-500 mr-2" />
            <h1 className="text-xl font-bold text-primary-700">Home Nurse</h1>
          </div>
          <button onClick={() => setIsSidebarOpen(false)}>
            <X size={24} className="text-neutral-500" />
          </button>
        </div>
        
        <div className="flex-1 px-4 py-6 space-y-1 overflow-y-auto">
          {navItems.map((item) => (
            <NavItem key={item.to} {...item} />
          ))}
        </div>
        
        <div className="p-4 border-t border-neutral-200">
          <button 
            onClick={handleLogout}
            className="flex items-center space-x-3 text-neutral-700 hover:text-primary-600 w-full px-4 py-3 rounded-lg transition-colors"
          >
            <LogOut size={20} />
            <span>Logout</span>
          </button>
        </div>
      </aside>
      
      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <header className="bg-white border-b border-neutral-200 py-3 px-4 flex items-center justify-between">
          <button 
            className="md:hidden text-neutral-500 hover:text-primary-500"
            onClick={() => setIsSidebarOpen(true)}
          >
            <Menu size={24} />
          </button>
          
          <div className="flex items-center ml-auto space-x-4">
            <button className="text-neutral-500 hover:text-primary-500 relative">
              <Bell size={20} />
              <span className="absolute top-0 right-0 w-2 h-2 bg-primary-500 rounded-full"></span>
            </button>
            
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 rounded-full bg-primary-100 flex items-center justify-center text-primary-700 font-medium">
                {currentUser?.displayName?.charAt(0) || 'U'}
              </div>
              <span className="text-sm font-medium hidden sm:block">
                {currentUser?.displayName || 'User'}
              </span>
            </div>
          </div>
        </header>
        
        {/* Page Content */}
        <main className="flex-1 p-6 overflow-y-auto">
          <Outlet />
        </main>
      </div>
    </div>
  );
};

export default DashboardLayout;