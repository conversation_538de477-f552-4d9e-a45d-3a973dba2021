import React from 'react';
import { Outlet, Link } from 'react-router-dom';
import { Heart } from 'lucide-react';

const AuthLayout: React.FC = () => {
  return (
    <div className="min-h-screen flex flex-col md:flex-row">
      {/* Left Side - Branding */}
      <div className="bg-primary-600 text-white md:w-1/2 p-8 flex flex-col justify-center">
        <div className="max-w-md mx-auto animate-fade-in">
          <div className="flex items-center mb-6">
            <Heart size={40} className="text-white mr-2" />
            <h1 className="text-3xl font-bold">Home Nurse</h1>
          </div>
          <h2 className="text-2xl font-semibold mb-4">Professional Healthcare at Your Doorstep</h2>
          <p className="text-primary-100 mb-8">
            Connect with professional nurses for personalized home care. Schedule appointments, 
            chat with healthcare providers, and manage your medical information all in one secure place.
          </p>
          
          <div className="bg-primary-700/30 p-6 rounded-lg backdrop-blur-sm">
            <h3 className="font-medium mb-3">Why choose Home Nurse?</h3>
            <ul className="space-y-2">
              <li className="flex items-start">
                <span className="mr-2">•</span>
                <span>Qualified healthcare professionals available 24/7</span>
              </li>
              <li className="flex items-start">
                <span className="mr-2">•</span>
                <span>Secure and private communication</span>
              </li>
              <li className="flex items-start">
                <span className="mr-2">•</span>
                <span>Easy appointment scheduling</span>
              </li>
              <li className="flex items-start">
                <span className="mr-2">•</span>
                <span>Comprehensive medical record management</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
      
      {/* Right Side - Auth Forms */}
      <div className="md:w-1/2 flex items-center justify-center p-8">
        <div className="w-full max-w-md animate-slide-up">
          <Outlet />
          
          <div className="mt-8 text-center text-sm text-neutral-500">
            <p>© 2025 Home Nurse. All rights reserved.</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AuthLayout;