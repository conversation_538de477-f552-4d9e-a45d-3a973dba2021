import React from 'react';
import { Info } from 'lucide-react';

const DemoNotice: React.FC = () => {
  return (
    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
      <div className="flex items-start">
        <Info size={20} className="text-blue-600 mr-3 mt-0.5 flex-shrink-0" />
        <div className="text-sm">
          <h4 className="font-medium text-blue-800 mb-1">Demo Mode Active</h4>
          <p className="text-blue-700">
            This app is running in demo mode. You can create accounts and log in without a real Firebase setup.
            All data is stored locally and will be cleared when you refresh the page.
          </p>
          <p className="text-blue-600 mt-2 font-medium">
            Try these demo credentials: <br />
            Email: <EMAIL> | Password: demo123
          </p>
          <p className="text-blue-600 mt-2 text-sm">
            <strong>Note:</strong> After registration, you'll need to verify your email with a 6-digit code
            that will be shown in an alert popup.
          </p>
        </div>
      </div>
    </div>
  );
};

export default DemoNotice;
