import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { useForm } from 'react-hook-form';
import { toast } from 'react-hot-toast';
import { KeyRound, Loader2, Arrow<PERSON>ef<PERSON> } from 'lucide-react';

interface ForgotPasswordInputs {
  email: string;
}

const ForgotPassword: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [isEmailSent, setIsEmailSent] = useState(false);
  const { resetPassword } = useAuth();
  const { register, handleSubmit, formState: { errors } } = useForm<ForgotPasswordInputs>();

  const onSubmit = async (data: ForgotPasswordInputs) => {
    try {
      setIsLoading(true);
      await resetPassword(data.email);
      setIsEmailSent(true);
      toast.success('Password reset email sent!');
    } catch (error: any) {
      let message = 'Failed to send password reset email';
      
      if (error.code === 'auth/user-not-found') {
        message = 'No account found with this email';
      }
      
      toast.error(message);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="w-full">
      <Link to="/login" className="inline-flex items-center text-primary-600 hover:text-primary-700 mb-6">
        <ArrowLeft size={16} className="mr-1" />
        Back to login
      </Link>
      
      <div className="text-center mb-8">
        <div className="flex justify-center mb-4">
          <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center">
            <KeyRound size={28} className="text-primary-600" />
          </div>
        </div>
        <h2 className="text-2xl font-bold text-neutral-800 mb-2">Reset Your Password</h2>
        <p className="text-neutral-500">
          {isEmailSent 
            ? "Check your email for password reset instructions" 
            : "Enter your email and we'll send you instructions to reset your password"}
        </p>
      </div>
      
      {isEmailSent ? (
        <div className="text-center">
          <div className="bg-green-50 p-4 rounded-lg border border-green-100 mb-6">
            <p className="text-sm text-green-800">
              A password reset link has been sent to your email address. Please check your inbox and follow the instructions.
            </p>
          </div>
          
          <button
            onClick={handleSubmit(onSubmit)}
            disabled={isLoading}
            className="btn btn-outline w-full mb-4"
          >
            Resend Reset Email
          </button>
          
          <Link to="/login" className="btn btn-primary w-full">
            Return to Login
          </Link>
        </div>
      ) : (
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div>
            <label htmlFor="email" className="form-label">Email Address</label>
            <input
              id="email"
              type="email"
              className="form-input"
              placeholder="<EMAIL>"
              {...register('email', { 
                required: 'Email is required',
                pattern: {
                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                  message: 'Invalid email address'
                }
              })}
            />
            {errors.email && <p className="form-error">{errors.email.message}</p>}
          </div>
          
          <button
            type="submit"
            disabled={isLoading}
            className="btn btn-primary w-full flex items-center justify-center"
          >
            {isLoading ? (
              <Loader2 size={20} className="animate-spin mr-2" />
            ) : null}
            Send Reset Instructions
          </button>
        </form>
      )}
    </div>
  );
};

export default ForgotPassword;