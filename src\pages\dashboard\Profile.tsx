import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { useForm } from 'react-hook-form';
import { toast } from 'react-hot-toast';
import { User, Lock, Mail, Upload, AlertTriangle, Loader2 } from 'lucide-react';

interface ProfileFormInputs {
  displayName: string;
  email: string;
  phone: string;
  address: string;
  emergencyContact: string;
  emergencyPhone: string;
}

interface PasswordFormInputs {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

const Profile: React.FC = () => {
  const { currentUser, updateUserProfile, getUserData } = useAuth();
  const [activeTab, setActiveTab] = useState<'profile' | 'security'>('profile');
  const [userData, setUserData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [avatarUrl, setAvatarUrl] = useState<string>('');
  
  const { register: registerProfile, handleSubmit: handleSubmitProfile, formState: { errors: profileErrors }, setValue } = 
    useForm<ProfileFormInputs>();
    
  const { register: registerPassword, handleSubmit: handleSubmitPassword, watch, formState: { errors: passwordErrors } } = 
    useForm<PasswordFormInputs>();

  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const data = await getUserData();
        setUserData(data);
        
        // Set form values
        if (data) {
          setValue('displayName', data.displayName || '');
          setValue('email', currentUser?.email || '');
          setValue('phone', data.phone || '');
          setValue('address', data.address || '');
          setValue('emergencyContact', data.emergencyContact || '');
          setValue('emergencyPhone', data.emergencyPhone || '');
        }
        
        // Set avatar URL
        setAvatarUrl(currentUser?.photoURL || '');
        
        setLoading(false);
      } catch (error) {
        console.error('Error fetching user data:', error);
        toast.error('Failed to load profile data');
        setLoading(false);
      }
    };
    
    fetchUserData();
  }, [currentUser, getUserData, setValue]);

  const onProfileSubmit = async (data: ProfileFormInputs) => {
    try {
      setIsSaving(true);
      
      // In a real app, you would update these fields in Firestore
      await updateUserProfile(data.displayName, avatarUrl);
      
      toast.success('Profile updated successfully');
    } catch (error) {
      console.error('Error updating profile:', error);
      toast.error('Failed to update profile');
    } finally {
      setIsSaving(false);
    }
  };

  const onPasswordSubmit = async (data: PasswordFormInputs) => {
    try {
      setIsSaving(true);
      
      // In a real app, you would update the password with Firebase Auth
      // This is just a simulation
      setTimeout(() => {
        toast.success('Password updated successfully');
        setIsSaving(false);
      }, 1000);
    } catch (error) {
      console.error('Error updating password:', error);
      toast.error('Failed to update password');
      setIsSaving(false);
    }
  };

  const handleAvatarChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // In a real app, you would handle file upload to Firebase Storage
    // This is just a simulation
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      const reader = new FileReader();
      
      reader.onloadend = () => {
        setAvatarUrl(reader.result as string);
      };
      
      reader.readAsDataURL(file);
    }
  };

  return (
    <div>
      <div className="bg-white rounded-lg shadow-sm p-4 mb-6">
        <h1 className="text-2xl font-bold">My Profile</h1>
        <p className="text-neutral-600">Manage your account information and settings</p>
      </div>
      
      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        <div className="flex border-b border-neutral-200">
          <button
            className={`px-6 py-3 font-medium text-sm flex items-center ${
              activeTab === 'profile'
                ? 'text-primary-600 border-b-2 border-primary-500'
                : 'text-neutral-600 hover:text-neutral-900'
            }`}
            onClick={() => setActiveTab('profile')}
          >
            <User size={16} className="mr-2" />
            Profile Information
          </button>
          
          <button
            className={`px-6 py-3 font-medium text-sm flex items-center ${
              activeTab === 'security'
                ? 'text-primary-600 border-b-2 border-primary-500'
                : 'text-neutral-600 hover:text-neutral-900'
            }`}
            onClick={() => setActiveTab('security')}
          >
            <Lock size={16} className="mr-2" />
            Security & Password
          </button>
        </div>
        
        <div className="p-6">
          {loading ? (
            <div className="flex justify-center items-center py-12">
              <Loader2 size={24} className="animate-spin text-primary-500" />
            </div>
          ) : activeTab === 'profile' ? (
            <form onSubmit={handleSubmitProfile(onProfileSubmit)}>
              <div className="flex flex-col md:flex-row md:space-x-8">
                {/* Avatar Section */}
                <div className="md:w-1/3 flex flex-col items-center mb-6 md:mb-0">
                  <div className="relative">
                    {avatarUrl ? (
                      <img
                        src={avatarUrl}
                        alt="Profile"
                        className="w-32 h-32 rounded-full object-cover border-4 border-neutral-200"
                      />
                    ) : (
                      <div className="w-32 h-32 rounded-full bg-primary-100 flex items-center justify-center text-primary-700 text-4xl font-medium border-4 border-neutral-200">
                        {currentUser?.displayName?.charAt(0) || 'U'}
                      </div>
                    )}
                    
                    <label
                      htmlFor="avatar-upload"
                      className="absolute bottom-0 right-0 bg-primary-500 text-white p-2 rounded-full cursor-pointer hover:bg-primary-600 transition-colors"
                    >
                      <Upload size={16} />
                      <input
                        id="avatar-upload"
                        type="file"
                        accept="image/*"
                        className="hidden"
                        onChange={handleAvatarChange}
                      />
                    </label>
                  </div>
                  
                  <div className="mt-4 text-center">
                    <h3 className="font-medium">{currentUser?.displayName || 'User'}</h3>
                    <p className="text-sm text-neutral-500">Patient</p>
                  </div>
                </div>
                
                {/* Profile Form */}
                <div className="md:w-2/3">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="col-span-2">
                      <label htmlFor="displayName" className="form-label">Full Name</label>
                      <input
                        id="displayName"
                        type="text"
                        className="form-input"
                        {...registerProfile('displayName', { required: 'Name is required' })}
                      />
                      {profileErrors.displayName && (
                        <p className="form-error">{profileErrors.displayName.message}</p>
                      )}
                    </div>
                    
                    <div>
                      <label htmlFor="email" className="form-label">Email Address</label>
                      <input
                        id="email"
                        type="email"
                        className="form-input bg-neutral-50"
                        disabled
                        {...registerProfile('email')}
                      />
                      <p className="text-xs text-neutral-500 mt-1">
                        Email cannot be changed
                      </p>
                    </div>
                    
                    <div>
                      <label htmlFor="phone" className="form-label">Phone Number</label>
                      <input
                        id="phone"
                        type="tel"
                        className="form-input"
                        placeholder="(*************"
                        {...registerProfile('phone')}
                      />
                    </div>
                    
                    <div className="col-span-2">
                      <label htmlFor="address" className="form-label">Home Address</label>
                      <input
                        id="address"
                        type="text"
                        className="form-input"
                        placeholder="123 Main St, City, State, ZIP"
                        {...registerProfile('address')}
                      />
                    </div>
                    
                    <div>
                      <label htmlFor="emergencyContact" className="form-label">Emergency Contact</label>
                      <input
                        id="emergencyContact"
                        type="text"
                        className="form-input"
                        placeholder="Contact name"
                        {...registerProfile('emergencyContact')}
                      />
                    </div>
                    
                    <div>
                      <label htmlFor="emergencyPhone" className="form-label">Emergency Phone</label>
                      <input
                        id="emergencyPhone"
                        type="tel"
                        className="form-input"
                        placeholder="(*************"
                        {...registerProfile('emergencyPhone')}
                      />
                    </div>
                  </div>
                  
                  <div className="mt-6 flex justify-end">
                    <button
                      type="submit"
                      disabled={isSaving}
                      className="btn btn-primary"
                    >
                      {isSaving ? (
                        <Loader2 size={16} className="animate-spin mr-2" />
                      ) : null}
                      Save Changes
                    </button>
                  </div>
                </div>
              </div>
            </form>
          ) : (
            <form onSubmit={handleSubmitPassword(onPasswordSubmit)}>
              <div className="max-w-lg mx-auto">
                <div className="mb-6 p-4 bg-yellow-50 border border-yellow-100 rounded-lg flex items-start">
                  <AlertTriangle size={20} className="text-yellow-500 mr-3 flex-shrink-0 mt-0.5" />
                  <p className="text-sm text-yellow-700">
                    Ensure your new password is at least 8 characters long and includes a mix of letters, numbers, and symbols for best security.
                  </p>
                </div>
                
                <div className="space-y-6">
                  <div>
                    <label htmlFor="currentPassword" className="form-label">Current Password</label>
                    <input
                      id="currentPassword"
                      type="password"
                      className="form-input"
                      placeholder="••••••••"
                      {...registerPassword('currentPassword', { 
                        required: 'Current password is required' 
                      })}
                    />
                    {passwordErrors.currentPassword && (
                      <p className="form-error">{passwordErrors.currentPassword.message}</p>
                    )}
                  </div>
                  
                  <div>
                    <label htmlFor="newPassword" className="form-label">New Password</label>
                    <input
                      id="newPassword"
                      type="password"
                      className="form-input"
                      placeholder="••••••••"
                      {...registerPassword('newPassword', { 
                        required: 'New password is required',
                        minLength: {
                          value: 8,
                          message: 'Password must be at least 8 characters'
                        }
                      })}
                    />
                    {passwordErrors.newPassword && (
                      <p className="form-error">{passwordErrors.newPassword.message}</p>
                    )}
                  </div>
                  
                  <div>
                    <label htmlFor="confirmPassword" className="form-label">Confirm New Password</label>
                    <input
                      id="confirmPassword"
                      type="password"
                      className="form-input"
                      placeholder="••••••••"
                      {...registerPassword('confirmPassword', { 
                        required: 'Please confirm your password',
                        validate: value => 
                          value === watch('newPassword') || 'Passwords do not match'
                      })}
                    />
                    {passwordErrors.confirmPassword && (
                      <p className="form-error">{passwordErrors.confirmPassword.message}</p>
                    )}
                  </div>
                </div>
                
                <div className="mt-8 flex justify-end">
                  <button
                    type="submit"
                    disabled={isSaving}
                    className="btn btn-primary"
                  >
                    {isSaving ? (
                      <Loader2 size={16} className="animate-spin mr-2" />
                    ) : null}
                    Update Password
                  </button>
                </div>
              </div>
            </form>
          )}
        </div>
      </div>
    </div>
  );
};

export default Profile;