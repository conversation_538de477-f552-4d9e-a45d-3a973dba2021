@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --primary-50: #e6f0fd;
  --primary-100: #cce0fb;
  --primary-200: #99c2f7;
  --primary-300: #66a3f3;
  --primary-400: #3385ef;
  --primary-500: #0066eb;
  --primary-600: #0052bc;
  --primary-700: #003d8d;
  --primary-800: #00295e;
  --primary-900: #00142f;

  --secondary-50: #e6f7f1;
  --secondary-100: #ccefe3;
  --secondary-200: #99dfc7;
  --secondary-300: #66cfab;
  --secondary-400: #33bf8f;
  --secondary-500: #00af73;
  --secondary-600: #008c5c;
  --secondary-700: #006945;
  --secondary-800: #00462e;
  --secondary-900: #002317;

  --accent-50: #feede6;
  --accent-100: #fddbcc;
  --accent-200: #fbb799;
  --accent-300: #f99366;
  --accent-400: #f66f33;
  --accent-500: #f44b00;
  --accent-600: #c33c00;
  --accent-700: #922d00;
  --accent-800: #611e00;
  --accent-900: #310f00;

  --neutral-50: #f8fafc;
  --neutral-100: #f1f5f9;
  --neutral-200: #e2e8f0;
  --neutral-300: #cbd5e1;
  --neutral-400: #94a3b8;
  --neutral-500: #64748b;
  --neutral-600: #475569;
  --neutral-700: #334155;
  --neutral-800: #1e293b;
  --neutral-900: #0f172a;

  --success: #22c55e;
  --warning: #f59e0b;
  --error: #ef4444;
}

body {
  font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 
    Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  color: var(--neutral-800);
  background-color: var(--neutral-50);
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2;
  }
  
  .btn-primary {
    @apply bg-[#0066eb] hover:bg-[#0052bc] text-white focus:ring-[#0066eb];
  }
  
  .btn-secondary {
    @apply bg-[#00af73] hover:bg-[#008c5c] text-white focus:ring-[#00af73];
  }
  
  .btn-accent {
    @apply bg-[#f44b00] hover:bg-[#c33c00] text-white focus:ring-[#f44b00];
  }
  
  .btn-outline {
    @apply border border-[#0066eb] text-[#0066eb] hover:bg-[#e6f0fd] focus:ring-[#0066eb];
  }
  
  .card {
    @apply bg-white rounded-lg shadow-sm p-6;
  }
  
  .form-input {
    @apply w-full px-3 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0066eb] focus:border-transparent;
  }
  
  .form-label {
    @apply block text-sm font-medium text-neutral-700 mb-1;
  }
  
  .form-error {
    @apply mt-1 text-sm text-[#ef4444];
  }
}

/* Custom animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

.animate-slide-up {
  animation: slideUp 0.4s ease-out;
}