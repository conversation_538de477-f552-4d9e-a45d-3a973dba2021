import React, { createContext, useContext, useState, useEffect } from 'react';
import { auth, db } from '../services/firebase';
import {
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  sendPasswordResetEmail,
  sendEmailVerification,
  signOut,
  updateProfile,
  onAuthStateChanged,
  User,
  ActionCodeSettings
} from 'firebase/auth';
import { doc, setDoc, getDoc } from 'firebase/firestore';

interface AuthContextType {
  currentUser: User | null;
  loading: boolean;
  register: (email: string, password: string, displayName: string) => Promise<User>;
  login: (email: string, password: string) => Promise<User>;
  logout: () => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  verifyEmail: () => Promise<void>;
  verifyEmailWithCode: (code: string) => Promise<boolean>;
  updateUserProfile: (displayName: string, photoURL: string) => Promise<void>;
  getUserData: () => Promise<any>;
}

const AuthContext = createContext<AuthContextType | null>(null);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  const register = async (email: string, password: string, displayName: string) => {
    try {
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);

      // Update profile with display name
      await updateProfile(userCredential.user, {
        displayName
      });

      // Create user document in Firestore (with error handling)
      try {
        await setDoc(doc(db, 'users', userCredential.user.uid), {
          displayName,
          email,
          createdAt: new Date().toISOString(),
          photoURL: '',
          role: 'patient'
        });
      } catch (firestoreError) {
        console.log('Firestore not available, skipping user document creation');
      }

      // Configure email verification settings
      const actionCodeSettings: ActionCodeSettings = {
        url: `${window.location.origin}/dashboard`, // URL to redirect to after verification
        handleCodeInApp: true,
      };

      // Send verification email with custom settings
      try {
        await sendEmailVerification(userCredential.user, actionCodeSettings);
        console.log('Verification email sent successfully');
      } catch (emailError) {
        console.log('Email verification not available in demo mode');
        // Don't throw error here - user is still created successfully
      }

      return userCredential.user;
    } catch (error: any) {
      // Handle Firebase errors gracefully
      if (error.code === 'auth/configuration-not-found' ||
          error.code === 'auth/invalid-api-key' ||
          error.message?.includes('demo-api-key')) {

        // Generate verification code for demo mode
        const verificationCode = Math.floor(100000 + Math.random() * 900000).toString();

        // Create a mock user for demo purposes (unverified initially)
        const mockUser = {
          uid: `demo-${Date.now()}`,
          email,
          displayName,
          emailVerified: false, // Start as unverified
          photoURL: null,
          phoneNumber: null,
          providerId: 'password',
          metadata: {
            creationTime: new Date().toISOString(),
            lastSignInTime: new Date().toISOString()
          }
        } as User;

        // Store in localStorage for demo
        localStorage.setItem('demoUser', JSON.stringify({
          email,
          password,
          displayName,
          uid: mockUser.uid,
          emailVerified: false,
          verificationCode
        }));
        localStorage.setItem('demoSession', 'pending-verification');

        // Set current user for immediate state update
        setCurrentUser(mockUser);

        console.log(`Demo mode: User registered successfully. Verification code: ${verificationCode}`);
        // In demo mode, show the verification code in console
        alert(`Demo Mode: Your verification code is: ${verificationCode}`);

        return mockUser;
      }

      throw error;
    }
  };

  const login = async (email: string, password: string) => {
    try {
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      return userCredential.user;
    } catch (error: any) {
      // Handle Firebase errors gracefully
      if (error.code === 'auth/configuration-not-found' ||
          error.code === 'auth/invalid-api-key' ||
          error.message?.includes('demo-api-key')) {

        // Check for demo user in localStorage
        const demoUserData = localStorage.getItem('demoUser');
        if (demoUserData) {
          const userData = JSON.parse(demoUserData);
          if (userData.email === email && userData.password === password) {
            // Create mock user for demo login
            const mockUser = {
              uid: userData.uid,
              email: userData.email,
              displayName: userData.displayName,
              emailVerified: userData.emailVerified || false, // Use stored verification status
              photoURL: null,
              phoneNumber: null,
              providerId: 'password',
              metadata: {
                creationTime: userData.creationTime || new Date().toISOString(),
                lastSignInTime: new Date().toISOString()
              }
            } as User;

            // Set demo session based on verification status
            if (userData.emailVerified) {
              localStorage.setItem('demoSession', 'active');
            } else {
              localStorage.setItem('demoSession', 'pending-verification');
            }

            // Set current user for immediate state update
            setCurrentUser(mockUser);

            console.log('Demo mode: User logged in successfully');
            return mockUser;
          }
        }

        // If no demo user found, throw appropriate error
        throw new Error('auth/user-not-found');
      }

      throw error;
    }
  };

  const logout = async () => {
    try {
      // Clear demo session
      localStorage.removeItem('demoSession');
      setCurrentUser(null);

      return signOut(auth);
    } catch (error) {
      // If Firebase signOut fails, just clear demo session
      localStorage.removeItem('demoSession');
      setCurrentUser(null);
      console.log('Demo mode: User logged out successfully');
    }
  };

  const resetPassword = async (email: string) => {
    // Configure password reset email settings
    const actionCodeSettings: ActionCodeSettings = {
      url: `${window.location.origin}/login`, // URL to redirect to after password reset
      handleCodeInApp: false,
    };

    try {
      await sendPasswordResetEmail(auth, email, actionCodeSettings);
      console.log('Password reset email sent successfully');
    } catch (error) {
      console.error('Error sending password reset email:', error);
      throw error;
    }
  };

  const verifyEmail = async () => {
    if (currentUser) {
      // Configure email verification settings
      const actionCodeSettings: ActionCodeSettings = {
        url: `${window.location.origin}/dashboard`, // URL to redirect to after verification
        handleCodeInApp: true,
      };

      try {
        await sendEmailVerification(currentUser, actionCodeSettings);
        console.log('Verification email resent successfully');
      } catch (error) {
        console.error('Error resending verification email:', error);
        // In demo mode, generate new code
        const demoUserData = localStorage.getItem('demoUser');
        if (demoUserData) {
          const userData = JSON.parse(demoUserData);
          const newVerificationCode = Math.floor(100000 + Math.random() * 900000).toString();
          userData.verificationCode = newVerificationCode;
          localStorage.setItem('demoUser', JSON.stringify(userData));
          alert(`Demo Mode: New verification code: ${newVerificationCode}`);
          return;
        }
        throw error;
      }
    } else {
      throw new Error('No user is currently logged in');
    }
  };

  const verifyEmailWithCode = async (code: string): Promise<boolean> => {
    if (!currentUser) {
      throw new Error('No user is currently logged in');
    }

    try {
      // In demo mode, check against stored verification code
      const demoUserData = localStorage.getItem('demoUser');
      if (demoUserData) {
        const userData = JSON.parse(demoUserData);
        if (userData.verificationCode === code) {
          // Mark user as verified
          userData.emailVerified = true;
          localStorage.setItem('demoUser', JSON.stringify(userData));
          localStorage.setItem('demoSession', 'active');

          // Update current user
          const updatedUser = { ...currentUser, emailVerified: true };
          setCurrentUser(updatedUser);

          console.log('Demo mode: Email verified successfully');
          return true;
        } else {
          return false;
        }
      }

      // For real Firebase, you would handle email verification differently
      // This is a simplified demo implementation
      return false;
    } catch (error) {
      console.error('Error verifying email:', error);
      return false;
    }
  };

  const updateUserProfile = async (displayName: string, photoURL: string) => {
    if (!currentUser) throw new Error('No user is currently logged in');

    await updateProfile(currentUser, {
      displayName: displayName || currentUser.displayName,
      photoURL: photoURL || currentUser.photoURL
    });

    // Update user document in Firestore
    await setDoc(doc(db, 'users', currentUser.uid), {
      displayName: displayName || currentUser.displayName,
      photoURL: photoURL || currentUser.photoURL
    }, { merge: true });
  };

  const getUserData = async () => {
    if (!currentUser) throw new Error('No user is currently logged in');

    const userDocRef = doc(db, 'users', currentUser.uid);
    const userDoc = await getDoc(userDocRef);

    if (userDoc.exists()) {
      return userDoc.data();
    }

    return null;
  };

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      setCurrentUser(user);
      setLoading(false);
    });

    // Create default demo user if none exists
    const initializeDemoUser = () => {
      const existingDemoUser = localStorage.getItem('demoUser');
      if (!existingDemoUser) {
        const defaultDemoUser = {
          email: '<EMAIL>',
          password: 'demo123',
          displayName: 'Demo User',
          uid: 'demo-default-user'
        };
        localStorage.setItem('demoUser', JSON.stringify(defaultDemoUser));
      }
    };

    // Also check for demo user in localStorage
    const checkDemoUser = () => {
      initializeDemoUser();

      const demoUserData = localStorage.getItem('demoUser');
      const demoSession = localStorage.getItem('demoSession');

      if (demoUserData && demoSession === 'active') {
        const userData = JSON.parse(demoUserData);
        const mockUser = {
          uid: userData.uid,
          email: userData.email,
          displayName: userData.displayName,
          emailVerified: true,
          photoURL: null,
          phoneNumber: null,
          providerId: 'password',
          metadata: {
            creationTime: userData.creationTime || new Date().toISOString(),
            lastSignInTime: new Date().toISOString()
          }
        } as User;

        setCurrentUser(mockUser);
      }
      setLoading(false);
    };

    // Check demo user if Firebase auth fails
    setTimeout(checkDemoUser, 1000);

    return unsubscribe;
  }, []);

  const value = {
    currentUser,
    loading,
    register,
    login,
    logout,
    resetPassword,
    verifyEmail,
    verifyEmailWithCode,
    updateUserProfile,
    getUserData
  };

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
};