import React from 'react';
import { Link } from 'react-router-dom';
import { Heart, Home } from 'lucide-react';

const NotFound: React.FC = () => {
  return (
    <div className="min-h-screen bg-neutral-50 flex flex-col items-center justify-center p-4">
      <div className="text-center">
        <div className="flex justify-center mb-6">
          <div className="relative">
            <Heart size={64} className="text-primary-500" />
            <span className="absolute inset-0 flex items-center justify-center text-white font-bold text-xl">
              404
            </span>
          </div>
        </div>
        
        <h1 className="text-3xl font-bold text-neutral-800 mb-2">Page Not Found</h1>
        <p className="text-neutral-600 mb-8 max-w-md mx-auto">
          The page you're looking for doesn't exist or has been moved.
        </p>
        
        <Link 
          to="/" 
          className="btn btn-primary inline-flex items-center"
        >
          <Home size={18} className="mr-2" />
          Back to Home
        </Link>
      </div>
    </div>
  );
};

export default NotFound;