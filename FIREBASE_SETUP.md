# Firebase Setup Guide for Home Nurse App

## 🔧 Quick Fix for Email Issues

The email functionality wasn't working because Firebase wasn't properly configured. Follow these steps to fix it:

## Option 1: Use Real Firebase Project (Recommended for Production)

### Step 1: Create Firebase Project
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Click "Create a project"
3. Enter project name (e.g., "home-nurse-app")
4. Enable Google Analytics (optional)
5. Create project

### Step 2: Enable Authentication
1. In Firebase Console, go to "Authentication"
2. Click "Get started"
3. Go to "Sign-in method" tab
4. Enable "Email/Password" provider
5. Save changes

### Step 3: Create Firestore Database
1. Go to "Firestore Database"
2. Click "Create database"
3. Choose "Start in test mode" (for development)
4. Select a location
5. Done

### Step 4: Get Firebase Configuration
1. Go to Project Settings (gear icon)
2. Scroll down to "Your apps"
3. Click "Web" icon (</>) to add web app
4. Register app with nickname
5. Copy the firebaseConfig object

### Step 5: Configure Environment Variables
1. Create `.env.local` file in project root
2. Add your Firebase config:

```env
VITE_FIREBASE_API_KEY=your_actual_api_key
VITE_FIREBASE_AUTH_DOMAIN=your_project_id.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your_project_id
VITE_FIREBASE_STORAGE_BUCKET=your_project_id.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=your_messaging_sender_id
VITE_FIREBASE_APP_ID=your_app_id
VITE_USE_FIREBASE_EMULATOR=false
```

## Option 2: Use Firebase Emulators (For Development/Testing)

### Step 1: Install Firebase CLI
```bash
npm install -g firebase-tools
```

### Step 2: Login to Firebase
```bash
firebase login
```

### Step 3: Initialize Firebase in Project
```bash
firebase init
```
- Select: Authentication, Firestore, Storage
- Choose existing project or create new one
- Accept default settings

### Step 4: Start Emulators
```bash
firebase emulators:start
```

### Step 5: Configure for Emulators
Create `.env.local`:
```env
VITE_USE_FIREBASE_EMULATOR=true
```

## 🚀 After Setup

1. Restart your development server:
```bash
npm run dev
```

2. Test email functionality:
   - Register a new account
   - Check console for email logs
   - Try password reset

## 📧 Email Configuration Notes

- **Real Firebase**: Emails will be sent to actual email addresses
- **Emulators**: Email links will be logged in console (no real emails sent)
- **Demo Config**: Will show errors but won't crash the app

## 🔍 Troubleshooting

### Email Not Received?
1. Check spam/junk folder
2. Verify email address is correct
3. Check Firebase Console > Authentication > Templates
4. Ensure email provider is enabled

### Still Having Issues?
1. Check browser console for errors
2. Verify Firebase configuration
3. Check Firebase Console for error logs
4. Ensure internet connection is stable

## 🎯 Current Status

✅ **Fixed Issues:**
- Added proper Firebase configuration with environment variables
- Enhanced email verification with custom action URLs
- Improved error handling for email operations
- Added fallback demo configuration

✅ **Email Features Working:**
- User registration with email verification
- Password reset emails
- Resend verification emails
- Custom redirect URLs after email actions

The app will now work with either a real Firebase project or in demo mode!
