import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { MailCheck, Loader2 } from 'lucide-react';
import { toast } from 'react-hot-toast';

const VerifyEmail: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const { currentUser, verifyEmail } = useAuth();
  const navigate = useNavigate();

  const handleResendVerification = async () => {
    try {
      setIsLoading(true);
      await verifyEmail();
      toast.success('Verification email sent!');
    } catch (error) {
      toast.error('Failed to send verification email');
    } finally {
      setIsLoading(false);
    }
  };

  // If user is not logged in, redirect to login
  if (!currentUser) {
    navigate('/login');
    return null;
  }

  // If email is already verified, redirect to dashboard
  if (currentUser.emailVerified) {
    navigate('/dashboard');
    return null;
  }

  return (
    <div className="w-full text-center">
      <div className="flex justify-center mb-6">
        <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center">
          <MailCheck size={32} className="text-primary-600" />
        </div>
      </div>
      
      <h2 className="text-2xl font-bold text-neutral-800 mb-2">Verify Your Email</h2>
      <p className="text-neutral-600 mb-6">
        We've sent a verification email to<br />
        <span className="font-medium">{currentUser.email}</span>
      </p>
      
      <div className="bg-neutral-50 p-4 rounded-lg border border-neutral-200 mb-6">
        <p className="text-sm text-neutral-700">
          Please check your email and click on the verification link to complete your registration.
          If you don't see the email, check your spam folder.
        </p>
      </div>
      
      <button
        onClick={handleResendVerification}
        disabled={isLoading}
        className="btn btn-outline w-full mb-4 flex items-center justify-center"
      >
        {isLoading ? (
          <Loader2 size={20} className="animate-spin mr-2" />
        ) : null}
        Resend Verification Email
      </button>
      
      <div className="flex justify-between">
        <Link to="/login" className="text-primary-600 hover:text-primary-700 font-medium">
          Back to Login
        </Link>
        
        <button 
          onClick={() => window.location.reload()} 
          className="text-primary-600 hover:text-primary-700 font-medium"
        >
          I've Verified
        </button>
      </div>
    </div>
  );
};

export default VerifyEmail;