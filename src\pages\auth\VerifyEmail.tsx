import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { Mail<PERSON>heck, Loader2 } from 'lucide-react';
import { toast } from 'react-hot-toast';
import { useForm } from 'react-hook-form';

interface VerifyFormInputs {
  verificationCode: string;
}

const VerifyEmail: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const { currentUser, verifyEmail, verifyEmailWithCode } = useAuth();
  const navigate = useNavigate();
  const { register, handleSubmit, formState: { errors } } = useForm<VerifyFormInputs>();

  const handleResendVerification = async () => {
    try {
      setIsResending(true);
      await verifyEmail();
      toast.success('New verification code sent!');
    } catch (error) {
      toast.error('Failed to send verification code');
    } finally {
      setIsResending(false);
    }
  };

  const onSubmit = async (data: VerifyFormInputs) => {
    try {
      setIsLoading(true);
      const isValid = await verifyEmailWithCode(data.verificationCode);

      if (isValid) {
        toast.success('Email verified successfully! Welcome to Home Nurse!');
        navigate('/dashboard');
      } else {
        toast.error('Invalid verification code. Please try again.');
      }
    } catch (error) {
      toast.error('Failed to verify email. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // If user is not logged in, redirect to login
  if (!currentUser) {
    navigate('/login');
    return null;
  }

  // If email is already verified, redirect to dashboard
  if (currentUser.emailVerified) {
    navigate('/dashboard');
    return null;
  }

  return (
    <div className="w-full text-center">
      <div className="flex justify-center mb-6">
        <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center">
          <MailCheck size={32} className="text-primary-600" />
        </div>
      </div>

      <h2 className="text-2xl font-bold text-neutral-800 mb-2">Verify Your Email</h2>
      <p className="text-neutral-600 mb-6">
        We've sent a verification code to<br />
        <span className="font-medium">{currentUser.email}</span>
      </p>

      <div className="bg-blue-50 p-4 rounded-lg border border-blue-200 mb-6">
        <p className="text-sm text-blue-700">
          <strong>Demo Mode:</strong> Your verification code was shown in an alert when you registered.
          If you missed it, click "Resend Code" below.
        </p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4 mb-6">
        <div>
          <label htmlFor="verificationCode" className="form-label">Verification Code</label>
          <input
            id="verificationCode"
            type="text"
            className="form-input text-center text-lg tracking-widest"
            placeholder="123456"
            maxLength={6}
            {...register('verificationCode', {
              required: 'Verification code is required',
              pattern: {
                value: /^\d{6}$/,
                message: 'Please enter a valid 6-digit code'
              }
            })}
          />
          {errors.verificationCode && <p className="form-error">{errors.verificationCode.message}</p>}
        </div>

        <button
          type="submit"
          disabled={isLoading}
          className="btn btn-primary w-full flex items-center justify-center"
        >
          {isLoading ? (
            <Loader2 size={20} className="animate-spin mr-2" />
          ) : null}
          Verify Email
        </button>
      </form>

      <button
        onClick={handleResendVerification}
        disabled={isResending}
        className="btn btn-outline w-full mb-4 flex items-center justify-center"
      >
        {isResending ? (
          <Loader2 size={20} className="animate-spin mr-2" />
        ) : null}
        Resend Verification Code
      </button>

      <div className="flex justify-center">
        <Link to="/login" className="text-primary-600 hover:text-primary-700 font-medium">
          Back to Login
        </Link>
      </div>
    </div>
  );
};

export default VerifyEmail;