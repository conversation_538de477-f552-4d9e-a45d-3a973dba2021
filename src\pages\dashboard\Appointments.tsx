import React, { useState, useEffect } from 'react';
import { Calendar as CalendarIcon, Clock, PlusCircle, Filter, MapPin, CheckCircle, XCircle } from 'lucide-react';
import Calendar from 'react-calendar';
import 'react-calendar/dist/Calendar.css';
import { format } from 'date-fns';

interface Appointment {
  id: string;
  nurseName: string;
  nurseSpecialty: string;
  date: Date;
  time: string;
  duration: string;
  location: string;
  status: 'upcoming' | 'completed' | 'canceled';
}

const Appointments: React.FC = () => {
  const [view, setView] = useState<'list' | 'calendar'>('list');
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [loading, setLoading] = useState(true);
  const [showScheduleForm, setShowScheduleForm] = useState(false);

  useEffect(() => {
    // Simulating API call
    setTimeout(() => {
      const demoAppointments: Appointment[] = [
        {
          id: '1',
          nurseName: '<PERSON>',
          nurseSpecialty: 'General Care',
          date: new Date(2025, 5, 15), // June 15, 2025
          time: '10:00 AM',
          duration: '45 min',
          location: 'Your Home',
          status: 'upcoming'
        },
        {
          id: '2',
          nurseName: 'Michael Chen',
          nurseSpecialty: 'Physical Therapy',
          date: new Date(2025, 5, 18), // June 18, 2025
          time: '2:30 PM',
          duration: '60 min',
          location: 'Your Home',
          status: 'upcoming'
        },
        {
          id: '3',
          nurseName: 'Amanda Wilson',
          nurseSpecialty: 'Wound Care',
          date: new Date(2025, 5, 8), // June 8, 2025
          time: '9:15 AM',
          duration: '30 min',
          location: 'Your Home',
          status: 'completed'
        },
        {
          id: '4',
          nurseName: 'Robert Davis',
          nurseSpecialty: 'Geriatric Care',
          date: new Date(2025, 5, 5), // June 5, 2025
          time: '3:00 PM',
          duration: '45 min',
          location: 'Your Home',
          status: 'canceled'
        }
      ];
      
      setAppointments(demoAppointments);
      setLoading(false);
    }, 1000);
  }, []);

  const filteredAppointments = appointments.filter(appointment => {
    if (filterStatus === 'all') return true;
    return appointment.status === filterStatus;
  });

  const dateHasAppointment = (date: Date) => {
    return appointments.some(appointment => 
      appointment.date.getDate() === date.getDate() &&
      appointment.date.getMonth() === date.getMonth() &&
      appointment.date.getFullYear() === date.getFullYear()
    );
  };

  const getAppointmentsForDate = (date: Date) => {
    return appointments.filter(appointment => 
      appointment.date.getDate() === date.getDate() &&
      appointment.date.getMonth() === date.getMonth() &&
      appointment.date.getFullYear() === date.getFullYear()
    );
  };

  const tileClassName = ({ date, view }: { date: Date; view: string }) => {
    if (view === 'month' && dateHasAppointment(date)) {
      return 'bg-primary-100 text-primary-700 rounded-full';
    }
    return null;
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'upcoming':
        return 'bg-primary-100 text-primary-700';
      case 'completed':
        return 'bg-green-100 text-green-700';
      case 'canceled':
        return 'bg-red-100 text-red-700';
      default:
        return 'bg-gray-100 text-gray-700';
    }
  };

  const formatAppointmentDate = (date: Date) => {
    return format(date, 'EEEE, MMMM d, yyyy');
  };

  return (
    <div>
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
        <h1 className="text-2xl font-bold text-neutral-800 mb-4 sm:mb-0">Appointments</h1>
        
        <div className="flex space-x-2">
          <button 
            onClick={() => setView('list')} 
            className={`btn ${view === 'list' ? 'btn-primary' : 'btn-outline'}`}
          >
            List
          </button>
          <button 
            onClick={() => setView('calendar')} 
            className={`btn ${view === 'calendar' ? 'btn-primary' : 'btn-outline'}`}
          >
            Calendar
          </button>
          <button 
            onClick={() => setShowScheduleForm(!showScheduleForm)} 
            className="btn btn-accent"
          >
            <PlusCircle size={16} className="mr-1" />
            Schedule
          </button>
        </div>
      </div>
      
      {showScheduleForm && (
        <div className="card mb-6 animate-fade-in">
          <h2 className="text-lg font-semibold mb-4">Schedule New Appointment</h2>
          <form className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="form-label">Nurse Type</label>
              <select className="form-input">
                <option>General Care</option>
                <option>Physical Therapy</option>
                <option>Wound Care</option>
                <option>Geriatric Care</option>
                <option>Pediatric Care</option>
              </select>
            </div>
            <div>
              <label className="form-label">Date</label>
              <input type="date" className="form-input" min={new Date().toISOString().split('T')[0]} />
            </div>
            <div>
              <label className="form-label">Time</label>
              <select className="form-input">
                <option>9:00 AM</option>
                <option>10:00 AM</option>
                <option>11:00 AM</option>
                <option>1:00 PM</option>
                <option>2:00 PM</option>
                <option>3:00 PM</option>
                <option>4:00 PM</option>
              </select>
            </div>
            <div>
              <label className="form-label">Duration</label>
              <select className="form-input">
                <option>30 minutes</option>
                <option>45 minutes</option>
                <option>60 minutes</option>
                <option>90 minutes</option>
              </select>
            </div>
            <div className="md:col-span-2">
              <label className="form-label">Notes (Optional)</label>
              <textarea className="form-input" rows={3} placeholder="Describe your needs or any special instructions"></textarea>
            </div>
            <div className="md:col-span-2 flex justify-end space-x-2">
              <button 
                type="button" 
                className="btn btn-outline"
                onClick={() => setShowScheduleForm(false)}
              >
                Cancel
              </button>
              <button type="submit" className="btn btn-primary">
                Request Appointment
              </button>
            </div>
          </form>
        </div>
      )}
      
      {/* Filter Options */}
      <div className="flex items-center space-x-2 mb-4">
        <Filter size={16} className="text-neutral-500" />
        <span className="text-sm font-medium">Filter:</span>
        <select
          value={filterStatus}
          onChange={(e) => setFilterStatus(e.target.value)}
          className="text-sm border border-neutral-300 rounded p-1"
        >
          <option value="all">All Appointments</option>
          <option value="upcoming">Upcoming</option>
          <option value="completed">Completed</option>
          <option value="canceled">Canceled</option>
        </select>
      </div>
      
      {loading ? (
        <div className="animate-pulse space-y-4">
          {[1, 2, 3].map((item) => (
            <div key={item} className="h-32 bg-white rounded-lg" />
          ))}
        </div>
      ) : view === 'list' ? (
        <>
          {filteredAppointments.length > 0 ? (
            <div className="space-y-4">
              {filteredAppointments.map((appointment) => (
                <div key={appointment.id} className="card hover:shadow-md transition-shadow">
                  <div className="flex flex-col sm:flex-row sm:items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center mb-2">
                        <h3 className="font-semibold text-lg">{appointment.nurseName}</h3>
                        <span className={`ml-3 px-2 py-0.5 text-xs rounded-full ${getStatusBadgeClass(appointment.status)}`}>
                          {appointment.status}
                        </span>
                      </div>
                      <p className="text-sm text-neutral-600">{appointment.nurseSpecialty}</p>
                      <div className="mt-2 space-y-1">
                        <div className="flex items-center text-sm">
                          <CalendarIcon size={14} className="text-neutral-500 mr-1" />
                          <span>{formatAppointmentDate(appointment.date)}</span>
                        </div>
                        <div className="flex items-center text-sm">
                          <Clock size={14} className="text-neutral-500 mr-1" />
                          <span>{appointment.time} • {appointment.duration}</span>
                        </div>
                        <div className="flex items-center text-sm">
                          <MapPin size={14} className="text-neutral-500 mr-1" />
                          <span>{appointment.location}</span>
                        </div>
                      </div>
                    </div>
                    
                    {appointment.status === 'upcoming' && (
                      <div className="mt-4 sm:mt-0 flex space-x-2">
                        <button className="btn btn-outline text-sm px-3 py-1">
                          Reschedule
                        </button>
                        <button className="btn btn-outline text-error text-sm px-3 py-1">
                          Cancel
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12 bg-white rounded-lg">
              <CalendarIcon size={48} className="mx-auto text-neutral-300 mb-4" />
              <h3 className="text-lg font-medium text-neutral-700 mb-1">No appointments found</h3>
              <p className="text-neutral-500 mb-4">
                {filterStatus === 'all' 
                  ? "You don't have any appointments scheduled."
                  : `You don't have any ${filterStatus} appointments.`}
              </p>
              <button 
                onClick={() => setShowScheduleForm(true)} 
                className="btn btn-primary"
              >
                Schedule New Appointment
              </button>
            </div>
          )}
        </>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-12 gap-6">
          <div className="md:col-span-7 bg-white p-4 rounded-lg shadow-sm">
            <Calendar 
              onChange={setSelectedDate} 
              value={selectedDate}
              tileClassName={tileClassName}
              className="w-full"
            />
          </div>
          
          <div className="md:col-span-5">
            <div className="bg-white p-4 rounded-lg shadow-sm">
              <h3 className="font-medium mb-3">
                {format(selectedDate, 'MMMM d, yyyy')}
              </h3>
              
              {getAppointmentsForDate(selectedDate).length > 0 ? (
                <div className="space-y-3">
                  {getAppointmentsForDate(selectedDate).map((appointment) => (
                    <div key={appointment.id} className="p-3 border border-neutral-200 rounded">
                      <div className="flex justify-between items-start">
                        <div>
                          <div className="font-medium">{appointment.nurseName}</div>
                          <div className="text-sm text-neutral-600">{appointment.nurseSpecialty}</div>
                        </div>
                        <div className={`px-2 py-0.5 text-xs rounded-full ${getStatusBadgeClass(appointment.status)}`}>
                          {appointment.status}
                        </div>
                      </div>
                      
                      <div className="mt-2 flex items-center text-sm">
                        <Clock size={14} className="text-neutral-500 mr-1" />
                        <span>{appointment.time} • {appointment.duration}</span>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-center py-4 text-neutral-500">
                  No appointments for this date
                </p>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Appointments;