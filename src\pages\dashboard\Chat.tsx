import React, { useState, useEffect, useRef } from 'react';
import { useParams } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { 
  Send, 
  Mic, 
  MicOff, 
  Image, 
  Paperclip, 
  MoreVertical, 
  Phone, 
  Video, 
  Search 
} from 'lucide-react';

interface ChatMessage {
  id: string;
  sender: string;
  senderId: string;
  content: string;
  timestamp: Date;
  isVoice?: boolean;
  voiceUrl?: string;
}

interface ChatContact {
  id: string;
  name: string;
  avatar: string;
  lastMessage: string;
  lastMessageTime: string;
  unread: number;
  online: boolean;
}

const Chat: React.FC = () => {
  const { id: contactId } = useParams<{ id: string }>();
  const { currentUser } = useAuth();
  const [message, setMessage] = useState('');
  const [isRecording, setIsRecording] = useState(false);
  const [selectedContact, setSelectedContact] = useState<ChatContact | null>(null);
  const [contacts, setContacts] = useState<ChatContact[]>([]);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [loading, setLoading] = useState(true);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Simulating API call to get contacts
    setTimeout(() => {
      const demoContacts: ChatContact[] = [
        {
          id: '1',
          name: 'Dr. Williams',
          avatar: 'https://images.pexels.com/photos/5452293/pexels-photo-5452293.jpeg?auto=compress&cs=tinysrgb&w=150',
          lastMessage: 'Your test results look good. Let\'s discuss in our next appointment.',
          lastMessageTime: '2h ago',
          unread: 1,
          online: true
        },
        {
          id: '2',
          name: 'Nurse Sarah',
          avatar: 'https://images.pexels.com/photos/5407206/pexels-photo-5407206.jpeg?auto=compress&cs=tinysrgb&w=150',
          lastMessage: 'Remember to take your medication as prescribed.',
          lastMessageTime: 'Yesterday',
          unread: 0,
          online: false
        },
        {
          id: '3',
          name: 'Michael Chen',
          avatar: 'https://images.pexels.com/photos/5327585/pexels-photo-5327585.jpeg?auto=compress&cs=tinysrgb&w=150',
          lastMessage: 'Looking forward to our therapy session tomorrow.',
          lastMessageTime: '2d ago',
          unread: 0,
          online: true
        }
      ];
      
      setContacts(demoContacts);
      
      // If contactId is provided in URL, select that contact
      if (contactId) {
        const contact = demoContacts.find(c => c.id === contactId);
        if (contact) {
          setSelectedContact(contact);
          fetchMessages(contactId);
        }
      }
      
      setLoading(false);
    }, 1000);
  }, [contactId]);

  const fetchMessages = (contactId: string) => {
    // Simulating API call to get messages
    setTimeout(() => {
      const now = new Date();
      const yesterday = new Date(now);
      yesterday.setDate(yesterday.getDate() - 1);
      
      const demoMessages: ChatMessage[] = [
        {
          id: '1',
          sender: 'Dr. Williams',
          senderId: '1',
          content: 'Hello! How are you feeling today?',
          timestamp: new Date(now.setHours(now.getHours() - 3))
        },
        {
          id: '2',
          sender: currentUser?.displayName || 'You',
          senderId: currentUser?.uid || 'user',
          content: 'I\'m feeling much better, thank you!',
          timestamp: new Date(now.setHours(now.getHours() - 2))
        },
        {
          id: '3',
          sender: 'Dr. Williams',
          senderId: '1',
          content: 'That\'s great to hear! Have you been taking your medication regularly?',
          timestamp: new Date(now.setHours(now.getHours() - 2))
        },
        {
          id: '4',
          sender: currentUser?.displayName || 'You',
          senderId: currentUser?.uid || 'user',
          content: 'Yes, I\'ve been following the schedule exactly as prescribed.',
          timestamp: new Date(now.setHours(now.getHours() - 1))
        },
        {
          id: '5',
          sender: 'Dr. Williams',
          senderId: '1',
          content: 'Perfect. Your test results look good. Let\'s discuss more details in our next appointment.',
          timestamp: new Date(now.setMinutes(now.getMinutes() - 30))
        }
      ];
      
      setMessages(demoMessages);
      scrollToBottom();
    }, 500);
  };

  const handleContactSelect = (contact: ChatContact) => {
    setSelectedContact(contact);
    fetchMessages(contact.id);
  };

  const handleSendMessage = () => {
    if (message.trim() === '') return;
    
    const newMessage: ChatMessage = {
      id: Date.now().toString(),
      sender: currentUser?.displayName || 'You',
      senderId: currentUser?.uid || 'user',
      content: message,
      timestamp: new Date()
    };
    
    setMessages([...messages, newMessage]);
    setMessage('');
    
    // Simulate reply after a delay
    if (selectedContact) {
      setTimeout(() => {
        const replyMessage: ChatMessage = {
          id: (Date.now() + 1).toString(),
          sender: selectedContact.name,
          senderId: selectedContact.id,
          content: 'Thank you for your message. I\'ll get back to you shortly.',
          timestamp: new Date()
        };
        
        setMessages(prev => [...prev, replyMessage]);
        scrollToBottom();
      }, 2000);
    }
    
    scrollToBottom();
  };

  const toggleVoiceRecording = () => {
    // In a real app, this would handle voice recording
    setIsRecording(!isRecording);
    
    if (isRecording) {
      // Simulate sending a voice message
      const newVoiceMessage: ChatMessage = {
        id: Date.now().toString(),
        sender: currentUser?.displayName || 'You',
        senderId: currentUser?.uid || 'user',
        content: 'Voice message',
        timestamp: new Date(),
        isVoice: true,
        voiceUrl: '#'
      };
      
      setMessages([...messages, newVoiceMessage]);
    }
  };

  const scrollToBottom = () => {
    setTimeout(() => {
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }, 100);
  };

  const formatMessageTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <div className="h-[calc(100vh-10rem)] flex flex-col">
      <div className="bg-white rounded-lg shadow-sm mb-4 p-4">
        <h1 className="text-xl font-bold">Messages</h1>
        <p className="text-neutral-600">Chat with your healthcare providers</p>
      </div>
      
      <div className="flex-1 bg-white rounded-lg shadow-sm overflow-hidden flex">
        {/* Contacts List */}
        <div className="w-72 border-r border-neutral-200 hidden md:block">
          <div className="p-3 border-b border-neutral-200">
            <div className="relative">
              <input
                type="text"
                placeholder="Search contacts..."
                className="w-full pl-8 pr-2 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500"
              />
              <Search size={16} className="absolute left-2.5 top-3 text-neutral-400" />
            </div>
          </div>
          
          <div className="overflow-y-auto h-[calc(100vh-16rem)]">
            {loading ? (
              <div className="p-4 space-y-3">
                {[1, 2, 3].map((item) => (
                  <div key={item} className="flex items-center space-x-3 animate-pulse">
                    <div className="w-10 h-10 rounded-full bg-neutral-200" />
                    <div className="flex-1">
                      <div className="h-4 bg-neutral-200 rounded w-1/2 mb-2" />
                      <div className="h-3 bg-neutral-200 rounded w-3/4" />
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div>
                {contacts.map((contact) => (
                  <button
                    key={contact.id}
                    className={`w-full text-left px-4 py-3 border-b border-neutral-100 hover:bg-neutral-50 flex items-start transition-colors ${
                      selectedContact?.id === contact.id ? 'bg-primary-50' : ''
                    }`}
                    onClick={() => handleContactSelect(contact)}
                  >
                    <div className="relative">
                      <img
                        src={contact.avatar}
                        alt={contact.name}
                        className="w-10 h-10 rounded-full object-cover"
                      />
                      {contact.online && (
                        <span className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 border-2 border-white rounded-full"></span>
                      )}
                    </div>
                    
                    <div className="ml-3 flex-1 overflow-hidden">
                      <div className="flex justify-between items-baseline">
                        <h3 className="font-medium truncate">{contact.name}</h3>
                        <span className="text-xs text-neutral-500 whitespace-nowrap">
                          {contact.lastMessageTime}
                        </span>
                      </div>
                      
                      <p className="text-sm text-neutral-600 truncate">
                        {contact.lastMessage}
                      </p>
                    </div>
                    
                    {contact.unread > 0 && (
                      <span className="ml-2 flex-shrink-0 w-5 h-5 bg-primary-500 text-white rounded-full text-xs flex items-center justify-center">
                        {contact.unread}
                      </span>
                    )}
                  </button>
                ))}
              </div>
            )}
          </div>
        </div>
        
        {/* Chat Area */}
        <div className="flex-1 flex flex-col">
          {selectedContact ? (
            <>
              {/* Chat Header */}
              <div className="p-3 border-b border-neutral-200 flex items-center justify-between">
                <div className="flex items-center">
                  <img
                    src={selectedContact.avatar}
                    alt={selectedContact.name}
                    className="w-10 h-10 rounded-full object-cover mr-3"
                  />
                  <div>
                    <h3 className="font-medium">{selectedContact.name}</h3>
                    <p className="text-xs text-neutral-500">
                      {selectedContact.online ? 'Online' : 'Offline'}
                    </p>
                  </div>
                </div>
                
                <div className="flex space-x-2">
                  <button className="p-2 text-neutral-600 hover:bg-neutral-100 rounded-full">
                    <Phone size={18} />
                  </button>
                  <button className="p-2 text-neutral-600 hover:bg-neutral-100 rounded-full">
                    <Video size={18} />
                  </button>
                  <button className="p-2 text-neutral-600 hover:bg-neutral-100 rounded-full">
                    <MoreVertical size={18} />
                  </button>
                </div>
              </div>
              
              {/* Messages */}
              <div className="flex-1 p-4 overflow-y-auto bg-neutral-50">
                <div className="space-y-4">
                  {messages.map((msg) => (
                    <div
                      key={msg.id}
                      className={`flex ${
                        msg.senderId === currentUser?.uid ? 'justify-end' : 'justify-start'
                      }`}
                    >
                      <div
                        className={`max-w-xs md:max-w-md rounded-lg p-3 ${
                          msg.senderId === currentUser?.uid
                            ? 'bg-primary-500 text-white rounded-br-none'
                            : 'bg-white text-neutral-800 rounded-bl-none shadow-sm'
                        }`}
                      >
                        {msg.isVoice ? (
                          <div className="flex items-center space-x-2">
                            <div className="w-36 h-10 bg-neutral-200 rounded flex items-center justify-center">
                              <span className="text-xs">Voice message</span>
                            </div>
                            <span className="text-xs">0:12</span>
                          </div>
                        ) : (
                          <p>{msg.content}</p>
                        )}
                        <div
                          className={`text-xs mt-1 ${
                            msg.senderId === currentUser?.uid ? 'text-primary-100' : 'text-neutral-500'
                          }`}
                        >
                          {formatMessageTime(msg.timestamp)}
                        </div>
                      </div>
                    </div>
                  ))}
                  <div ref={messagesEndRef} />
                </div>
              </div>
              
              {/* Message Input */}
              <div className="p-3 border-t border-neutral-200 bg-white">
                <div className="flex items-center space-x-2">
                  <button className="p-2 text-neutral-600 hover:bg-neutral-100 rounded-full">
                    <Paperclip size={20} />
                  </button>
                  <button className="p-2 text-neutral-600 hover:bg-neutral-100 rounded-full">
                    <Image size={20} />
                  </button>
                  
                  <div className="flex-1 relative">
                    <input
                      type="text"
                      value={message}
                      onChange={(e) => setMessage(e.target.value)}
                      placeholder="Type a message..."
                      className="w-full pl-3 pr-10 py-2 border border-neutral-300 rounded-full focus:outline-none focus:ring-1 focus:ring-primary-500"
                      onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                    />
                  </div>
                  
                  {message.trim() ? (
                    <button
                      onClick={handleSendMessage}
                      className="p-2 bg-primary-500 text-white rounded-full hover:bg-primary-600"
                    >
                      <Send size={20} />
                    </button>
                  ) : (
                    <button
                      onClick={toggleVoiceRecording}
                      className={`p-2 rounded-full ${
                        isRecording
                          ? 'bg-red-500 text-white animate-pulse'
                          : 'bg-primary-500 text-white'
                      }`}
                    >
                      {isRecording ? <MicOff size={20} /> : <Mic size={20} />}
                    </button>
                  )}
                </div>
              </div>
            </>
          ) : (
            <div className="flex-1 flex flex-col items-center justify-center bg-neutral-50 p-4">
              <div className="text-center">
                <MessageSquare size={48} className="mx-auto text-neutral-300 mb-4" />
                <h3 className="text-lg font-medium text-neutral-700 mb-1">Your Messages</h3>
                <p className="text-neutral-500 mb-4 max-w-md">
                  Select a conversation or start a new one with your healthcare providers
                </p>
                
                <div className="md:hidden">
                  <h4 className="font-medium mb-2">Contacts</h4>
                  <div className="space-y-2">
                    {contacts.map((contact) => (
                      <button
                        key={contact.id}
                        className="w-full p-2 border border-neutral-200 rounded-lg hover:bg-white transition-colors flex items-center"
                        onClick={() => handleContactSelect(contact)}
                      >
                        <img
                          src={contact.avatar}
                          alt={contact.name}
                          className="w-8 h-8 rounded-full object-cover mr-2"
                        />
                        <span>{contact.name}</span>
                        {contact.unread > 0 && (
                          <span className="ml-auto w-5 h-5 bg-primary-500 text-white rounded-full text-xs flex items-center justify-center">
                            {contact.unread}
                          </span>
                        )}
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Chat;